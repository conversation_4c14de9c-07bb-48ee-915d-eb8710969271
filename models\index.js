import * as UserModel from './user.model.js';
import * as SessionModel from './session.model.js';
import * as RefreshTokenModel from './refreshToken.model.js';
import * as LoginAttemptModel from './loginAttempt.model.js';
import * as OtpCodeModel from './otpCode.model.js';
import * as DomainModel from './domain.model.js';
import * as SubjectModel from './subject.model.js';
import * as SpecializationModel from './specialization.model.js';
import * as InstructorModel from './instructor.model.js';
import * as CourseModel from './course.model.js';
import * as CourseLevelModel from './courseLevel.model.js';
import * as LessonModel from './lesson.model.js';
import * as AccessCodeModel from './accessCode.model.js';
import * as CodeRequestModel from './codeRequest.model.js';
import * as CourseProgressModel from './courseProgress.model.js';
import * as LessonProgressModel from './lessonProgress.model.js';
import * as QuizModel from './quiz.model.js';
import * as QuestionModel from './question.model.js';
import * as OptionModel from './option.model.js';
import * as QuizResultModel from './quizResult.model.js';
import * as NotificationModel from './notification.model.js';

export {
  UserModel,
  SessionModel,
  RefreshTokenModel,
  LoginAttemptModel,
  OtpCodeModel,
  DomainModel,
  SpecializationModel,
  SubjectModel,
  InstructorModel,
  CourseModel,
  CourseLevelModel,
  LessonModel,
  AccessCodeModel,
  CodeRequestModel,
  CourseProgressModel,
  LessonProgressModel,
  QuizModel,
  QuestionModel,
  OptionModel,
  QuizResultModel,
  NotificationModel,
};
