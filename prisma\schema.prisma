generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// ---------- Enums ----------

enum Role {
  STUDENT
  ADMIN
  SUBADMIN
}


// ---------- Auth / Sessions / Security ----------
model User {
  id           Int       @id @default(autoincrement())
  phone        String    @unique
  name         String
  birthDate    DateTime
  avatarUrl    String?
  role         Role      @default(STUDENT)
  sex          String
  country      String? // سيتم ملؤه تلقائياً من رقم الهاتف
  countryCode  String? // رمز الدولة (+966, +20, إلخ)
  isVerified   Boolean   @default(false)
  isActive     Boolean   @default(true)
  expiresAt    DateTime?
  fcmToken     String? // Firebase Cloud Messaging token for push notifications
  accessCodes  AccessCode[]
  lessonProgress LessonProgress[]

  currentSessionId String?        @unique
  sessions         Session[]
  refreshTokens    RefreshToken[]
  loginAttempts    LoginAttempt[]

  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  QuizResult     QuizResult[]
  CourseProgress CourseProgress[]
  Notification   Notification[]
  Review         Review[]
  SupportMessage SupportMessage[]
  Suggestion     Suggestion[]
  AuditLog       AuditLog[]
  CodeRequest    CodeRequest[]
  Admin          Admin[]

  @@map("user")
}

model Admin {
  id           Int       @id @default(autoincrement())
  userId       Int
  user         User      @relation(fields: [userId], references: [id])
  username     String    @unique
  email        String    @unique
  passwordHash String

  @@map("admin")
}

model Session {
  id         String    @id @default(cuid())
  user       User      @relation(fields: [userId], references: [id])
  userId     Int
  userAgent  String?
  ip         String?
  realIp     String? // عنوان IP الحقيقي (بعد معالجة proxy headers)
  location   String? // معلومات الموقع الجغرافي (اختياري)
  deviceInfo String? // معلومات الجهاز (اختياري)
  createdAt  DateTime  @default(now())
  revokedAt  DateTime?

  refreshTokens RefreshToken[]

  @@index([userId])
  @@index([realIp])
  @@map("session")
}

model RefreshToken {
  id        Int       @id @default(autoincrement())
  tokenHash String    @unique // hash للتوكن للأمان
  user      User      @relation(fields: [userId], references: [id])
  userId    Int
  session   Session   @relation(fields: [sessionId], references: [id])
  sessionId String
  expiresAt DateTime
  isRevoked Boolean   @default(false)
  revokedAt DateTime?
  createdAt DateTime  @default(now())

  @@index([userId])
  @@index([sessionId])
  @@index([tokenHash])
  @@map("refreshToken")
}

model LoginAttempt {
  id            Int      @id @default(autoincrement())
  identifier    String // رقم الهاتف أو البريد الإلكتروني
  user          User?    @relation(fields: [userId], references: [id])
  userId        Int?
  ip            String
  userAgent     String?
  success       Boolean  @default(false)
  failureReason String? // سبب الفشل (كلمة مرور خاطئة، حساب مقفل، إلخ)
  createdAt     DateTime @default(now())

  @@index([identifier])
  @@index([ip])
  @@index([createdAt])
  @@map("loginAttempt")
}

model OtpCode {
  id          Int      @id @default(autoincrement())
  phone       String
  code        String
  expiresAt   DateTime
  used        Boolean  @default(false)
  attempts    Int      @default(0) // عدد محاولات التحقق
  maxAttempts Int      @default(3) // الحد الأقصى للمحاولات

  createdAt DateTime @default(now())

  @@index([phone])
  @@map("otpCode")
}

// ---------- Content taxonomy ----------
model Domain {
  id              Int              @id @default(autoincrement())
  name            String
  slug            String?          @unique
  isActive        Boolean          @default(true)
  subjects        Subject[]
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  @@map("domain")
}

model Subject {
  id               Int            @id @default(autoincrement())
  name             String
  slug             String?        @unique
  isActive         Boolean        @default(true)
  domainId         Int?
  domain           Domain?         @relation(fields: [domainId], references: [id])
  specialization   Specialization[]
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt

  @@index([domainId])
  @@map("subject")
}

model Specialization {
  id           Int      @id @default(autoincrement())
  name         String
  slug         String?   @unique
  imageUrl     String?
  isActive     Boolean   @default(true)
  subjectId    Int?
  subject      Subject?         @relation(fields: [subjectId], references: [id])
  instructors  Instructor[]
  courses      Course[]
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  @@index([subjectId])
  @@map("specialization")
}

model Course {
  id                   Int              @id @default(autoincrement())
  title                String
  slug                 String?          @unique
  description          String?
  imageUrl             String?
  specializationId     Int
  specialization       Specialization @relation(fields: [specializationId], references: [id])
  isActive             Boolean          @default(true)
  levels               CourseLevel[]
  createdAt            DateTime         @default(now())
  updatedAt            DateTime         @updatedAt

  @@index([specializationId])
  @@map("course")
}

model Instructor {
  id        Int      @id @default(autoincrement())
  name      String
  bio       String?
  avatarUrl String?
  specializationId Int
  specialization   Specialization @relation(fields: [specializationId], references: [id])
  // courses    Course[]
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  courseLevels CourseLevel[]

  @@index([specializationId])
  @@map("instructor")
}



model CourseLevel {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  order       Int?
  priceUSD    Float?
  priceSAR    Float?
  isFree      Boolean          @default(false)
  isActive    Boolean          @default(true)
  imageUrl    String?
  previewUrl  String?
  downloadUrl String?
  courseId    Int
  course      Course   @relation(fields: [courseId], references: [id])
  instructorId  Int
  instructor    Instructor  @relation(fields: [instructorId], references: [id])
  lessons      Lesson[]
  questions    Question[]
  quizResults  QuizResult[]
  progress     CourseProgress[]
  reviews      Review[]
  accessCodes  AccessCode[]
  CodeRequest  CodeRequest[]
  coupons      Coupon[]
  files        File[]
  suggestions  Suggestion[]
  createdAt   DateTime @default  (now())
  updatedAt   DateTime @updatedAt

  @@index([courseId])
  @@index([instructorId])
  @@map("courseLevel")
}

model Lesson {
  id                Int              @id @default(autoincrement())
  title             String
  description       String?
  youtubeUrl        String?
  youtubeId         String?
  googleDriveUrl    String?
  durationSec       Int?
  orderIndex        Int?
  isFreePreview     Boolean          @default(false)
  isActive          Boolean          @default(true)
  courseLevelId     Int
  courseLevel       CourseLevel     @relation(fields: [courseLevelId], references: [id])
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  progress          LessonProgress[]

  @@index([courseLevelId])
  @@map("lesson")
}



model AccessCode {
  id        Int       @id @default(autoincrement())
  code      String    @unique
  courseLevelId Int
  courseLevel CourseLevel @relation(fields: [courseLevelId], references: [id])
  validityInMonths Int?
  issuedBy  Int?
  issuedAt  DateTime  @default(now())
  usedBy    Int
  user      User     @relation(fields: [usedBy], references: [id])
  used      Boolean   @default(false)
  usedAt    DateTime?
  expiresAt DateTime?
  isActive  Boolean   @default(true)
  transaction Transaction[]

  @@index([courseLevelId])
  @@index([usedBy])
  @@map("accessCode")
}

// ---------- Access / Purchase /  yments ----------
model Transaction {
 id              Int       @id @default(autoincrement())
  receiptImageUrl String
  amountPaid      Decimal   @db.Decimal(12, 2)
  notes           String?
  accessCodeId    Int
  accessCode      AccessCode @relation(fields: [accessCodeId], references: [id])
  couponId        Int?
  coupon          Coupon?    @relation(fields: [couponId], references: [id])
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@index([accessCodeId])
  @@index([couponId])
  @@map("transaction")
}

model CodeRequest {
  id        Int      @id @default(autoincrement())
  userId    Int
  user      User     @relation(fields: [userId], references: [id])
  courseLevelId  Int
  courseLevel    CourseLevel   @relation(fields: [courseLevelId], references: [id])
  status    String   @default("PENDING") // PENDING, APPROVED, REJECTED
  contact   String? // رقم واتساب أو تيليغرام المستخدم
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([courseLevelId])
  @@map("codeRequest")
}


model Coupon {
  id        Int       @id @default(autoincrement())
  code      String    @unique
  discount  Float
  isPercent Boolean   @default(true)
  expiry    DateTime?
  maxUsage  Int?
  usedCount Int       @default(0)
  isActive  Boolean   @default(true)
  courseLevelId Int
  courseLevel   CourseLevel @relation(fields: [courseLevelId], references: [id])
  transaction Transaction[]
  createdBy Int?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  @@index([courseLevelId])
  @@map("coupon")
}

// ---------- Quizzes & Results ----------

model Question {
  id            Int      @id @default(autoincrement())
  courseLevelId Int
  courseLevel   CourseLevel @relation(fields: [courseLevelId], references: [id])
  text          String
  order         Int?
  options       Option[]

  @@map("question")
}

model Option {
  id         Int      @id @default(autoincrement())
  questionId Int
  question   Question @relation(fields: [questionId], references: [id])
  text       String
  isCorrect  Boolean  @default(false)

  @@map("option")
}

model QuizResult {
  id            Int      @id @default(autoincrement())
  userId        Int
  user          User     @relation(fields: [userId], references: [id])
  courseLevelId Int
  courseLevel   CourseLevel @relation(fields: [courseLevelId], references: [id])
  score         Float
  answers       Json
  createdAt     DateTime @default(now())

  @@index([userId])
  @@index([courseLevelId])
  @@map("quizResult")
}

model CourseProgress {
  id        Int      @id @default(autoincrement())
  userId    Int
  user      User     @relation(fields: [userId], references: [id])
  courseLevelId  Int
  courseLevel    CourseLevel   @relation(fields: [courseLevelId], references: [id])
  progress  Float    @default(0)
  completed Boolean  @default(false)
  score     Float?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, courseLevelId])
  @@map("courseProgress")
}

model LessonProgress {
  id        Int      @id @default(autoincrement())
  userId    Int
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  lessonId  Int
  lesson    Lesson   @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  completed Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, lessonId])
  @@index([lessonId])
  @@map("lessonProgress")
}

// ---------- Notifications / Stories / Ads ----------
enum NotificationType {
  GENERAL
  COURSE_NEW
  COURSE_UPDATE
  LESSON_NEW
  QUIZ_AVAILABLE
  SYSTEM
}

model Notification {
  id        Int              @id @default(autoincrement())
  userId    Int?
  user      User?            @relation(fields: [userId], references: [id])
  title     String
  body      String
  type      NotificationType @default(GENERAL)
  data      Json?
  isRead    Boolean          @default(false)
  link      String?
  imageUrl  String?
  sentToFCM Boolean          @default(false)
  createdAt DateTime         @default(now())

  @@index([userId])
  @@index([createdAt])
  @@index([type])
  @@map("notification")
}

model Story {
  id         Int       @id @default(autoincrement())
  title      String?
  imageUrl   String
  startedAt  DateTime?
  endedAt    DateTime?
  orderIndex Int?
  isActive   Boolean   @default(true)
  createdAt  DateTime  @default(now())

  @@map("story")
}

model Advertisement {
  id        Int       @id @default(autoincrement())
  title     String
  imageUrl  String
  link      String?
  isActive  Boolean   @default(true)
  startsAt  DateTime?
  endsAt    DateTime?
  createdAt DateTime  @default(now())

  @@map("advertisement")
}

// ---------- Ratings / Feedback / Support ----------
model Review {
  id        Int      @id @default(autoincrement())
  userId    Int
  user      User     @relation(fields: [userId], references: [id])
  courseLevelId  Int
  courseLevel    CourseLevel   @relation(fields: [courseLevelId], references: [id])
  rating    Int
  comment   String?
  createdAt DateTime @default(now())

  @@index([userId])
  @@index([courseLevelId])
  @@map("review")
}

model SupportMessage {
  id        Int      @id @default(autoincrement())
  userId    Int
  user      User     @relation(fields: [userId], references: [id])
  message   String
  reply     String?
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())

  @@index([userId])
  @@map("supportMessage")
}

model Suggestion {
  id              Int           @id @default(autoincrement())
  userId          Int
  user            User          @relation(fields: [userId], references: [id])
  courseLevelId   Int?
  courseLevel     CourseLevel?   @relation(fields: [courseLevelId], references: [id])
  message         String
  createdAt       DateTime      @default(now())

  @@index([userId])
  @@index([courseLevelId])
  @@map("suggestion")
}

// ---------- Utilities / Admin logs / Settings ----------
model File {
  id            Int         @id @default(autoincrement())
  key           String      @unique
  url           String
  name          String?
  type          String?
  size          Int?
  meta          Json?
  courseLevelId Int?
  courseLevel   CourseLevel? @relation(fields: [courseLevelId], references: [id])
  createdAt     DateTime    @default(now())

  @@index([courseLevelId])
  @@map("file")
}

model AuditLog {
  id        Int      @id @default(autoincrement())
  actorId   Int?
  actor     User?    @relation(fields: [actorId], references: [id])
  action    String
  resource  String?
  meta      Json?
  createdAt DateTime @default(now())

  @@map("audit_log")
}

model RateLimit {
  id        Int       @id @default(autoincrement())
  key       String
  attempts  Int       @default(0)
  windowEnd DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  @@index([key])
  @@map("rate_limit")
}

//------------ settings --------------

model AppSettings {
  id        Int      @id @default(autoincrement())
  key       String   @unique
  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("app_settings")
}
