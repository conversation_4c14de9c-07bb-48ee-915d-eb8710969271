import { Router } from 'express';
import { requireAuth } from '../middlewares/auth.middleware.js';
import { requireRole } from '../middlewares/role.middleware.js';
import { validate } from '../middlewares/validate.middleware.js';
import { uploadNoticeImage } from '../middlewares/upload.middleware.js';
import {
  adminGenerateCodes, adminGetAllCodes, adminGetCourseCodes, adminGetCodesByUserId,
  studentGetMyCodes, studentGetMyCourses, studentActivateCode
} from '../controllers/accessCode.controller.js';
import { generateCodesRules, activateCodeRules } from '../validators/accessCode.validators.js';
import { idParam ,courseIdParam, courseLevelIdParam } from '../validators/catalog.validators.js';

const router = Router();

// --- Admin Routes ---
router.post('/admin/generate', requireAuth, requireRole(['ADMIN', 'SUBADMIN']), uploadNoticeImage.single('receiptImageUrl'), validate(generateCodesRules), adminGenerateCodes);
router.get('/admin/course/:courseId', requireAuth, requireRole(['ADMIN', 'SUBADMIN']), validate(courseIdParam), adminGetCourseCodes);
router.get('/admin/all', requireAuth, requireRole(['ADMIN', 'SUBADMIN']), adminGetAllCodes);
router.get('/admin/user/:id', requireAuth, requireRole(['ADMIN', 'SUBADMIN']), validate(idParam), adminGetCodesByUserId);

// --- Student Routes ---
router.get('/my-codes', requireAuth, requireRole(['STUDENT']), studentGetMyCodes);
router.get('/my-courses', requireAuth, requireRole(['STUDENT']), studentGetMyCourses);
router.post('/activate/level/:courseLevelId', requireAuth, requireRole(['STUDENT']), validate(courseLevelIdParam), validate(activateCodeRules), studentActivateCode);

export default router;